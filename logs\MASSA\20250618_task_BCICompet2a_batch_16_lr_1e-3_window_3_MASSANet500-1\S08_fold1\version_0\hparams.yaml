args: !!python/object/new:easydict.EasyDict
  dictitems:
    BASE_PATH: ./Datasets/BCI_Competition_IV/BCI_Competition_IV_2a_Raw
    CKPT_PATH: ./checkpoints
    EPOCHS: 500
    GPU_NUM: '0'
    LOG_NAME: ********_task_BCICompet2a_batch_16_lr_1e-3_window_3_MASSANet500-1
    LOG_PATH: ./logs
    SEED: 42
    bank: &id001
    - - - 4
        - 16
    - - - 16
        - 40
    batch_size: 16
    current_time: '********'
    device: &id002 !!python/object/apply:torch.device
    - cuda
    downsampling: 0
    filter_bank: false
    is_test: false
    k_folds: 5
    kernel_num: 8
    log_etc: MASSANet500-1
    lr: 0.001
    mixup_alpha: 0.3
    momentum: 0.9
    num_channels: 22
    num_classes: 4
    num_subjects: 9
    num_workers: 0
    sampling_rate: 250s
    smoothing: 0.05
    target_subject: 8
    task: BCICompet2a
    weight_decay: 0.075
    window_length: 3
  state:
    BASE_PATH: ./Datasets/BCI_Competition_IV/BCI_Competition_IV_2a_Raw
    CKPT_PATH: ./checkpoints
    EPOCHS: 500
    GPU_NUM: '0'
    LOG_NAME: ********_task_BCICompet2a_batch_16_lr_1e-3_window_3_MASSANet500-1
    LOG_PATH: ./logs
    SEED: 42
    bank: *id001
    batch_size: 16
    current_time: '********'
    device: *id002
    downsampling: 0
    filter_bank: false
    is_test: false
    k_folds: 5
    kernel_num: 8
    log_etc: MASSANet500-1
    lr: 0.001
    mixup_alpha: 0.3
    momentum: 0.9
    num_channels: 22
    num_classes: 4
    num_subjects: 9
    num_workers: 0
    sampling_rate: 250s
    smoothing: 0.05
    target_subject: 8
    task: BCICompet2a
    weight_decay: 0.075
    window_length: 3
