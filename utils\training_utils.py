import torch
import torch.optim as optim
import numpy as np
import torch.nn as nn
import torch.nn.functional as F
from pytorch_lightning.callbacks import ModelCheckpoint, LearningRateMonitor

def mixup_data(x, y, alpha=0.4):
    """Returns mixed inputs, pairs of targets, and lambda."""
    if alpha > 0:
        lam = np.random.beta(alpha, alpha)
    else:
        lam = 1.0
    batch_size = x.size(0)
    index = torch.randperm(batch_size).to(x.device)
    mixed_x = lam * x + (1 - lam) * x[index]
    y_a, y_b = y, y[index]
    return mixed_x, y_a, y_b, lam

class LabelSmoothingLoss(nn.Module):
    """标签平滑交叉熵（输入为 logits）。"""
    def __init__(self, classes, smoothing=0.1):
        super().__init__()
        assert 0.0 <= smoothing < 1.0
        self.confidence = 1.0 - smoothing
        self.smoothing = smoothing
        self.cls = classes

    def forward(self, pred, target):
        """
        pred: [B, C] logits
        target: [B] 真实标签
        """
        log_preds = F.log_softmax(pred, dim=-1)
        with torch.no_grad():
            # 构造平滑后的真实分布
            true_dist = torch.zeros_like(log_preds)
            true_dist.fill_(self.smoothing / (self.cls - 1))
            true_dist.scatter_(1, target.unsqueeze(1), self.confidence)
        return torch.mean(torch.sum(-true_dist * log_preds, dim=-1))


def get_criterion():
    
    criterion = torch.nn.CrossEntropyLoss()
    
    return criterion


def get_optimizer(model, args):
    # 添加梯度裁剪支持
    if hasattr(args, 'gradient_clip') and args.gradient_clip > 0:
        # 在训练循环中会使用gradient_clip_val参数
        pass

    optimizer = optim.Adam(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)

    return optimizer


def get_scheduler(optimizer, args):
    # 支持不同类型的学习率调度器
    if hasattr(args, 'lr_scheduler'):
        if args.lr_scheduler == 'cosine':
            scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.EPOCHS, eta_min=args.lr * 0.01)
        elif args.lr_scheduler == 'step':
            scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=args.EPOCHS//3, gamma=0.5)
        elif args.lr_scheduler == 'plateau':
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='max', factor=0.5, patience=5)
        else:
            scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.EPOCHS, eta_min=0)
    else:
        scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.EPOCHS, eta_min=0)

    return scheduler


def get_checkpoint_callback(fold: int, monitor: str, args):
    if monitor == 'val_acc':
        return ModelCheckpoint(monitor=monitor,
                                dirpath=f'{args.CKPT_PATH}/{args.LOG_NAME}/fold_{fold + 1}',
                                filename=f'{args.task}_S{args.target_subject:02d}_' + '{epoch:02d}-{val_acc:.3f}',
                                save_top_k=3,
                                mode='max')
    elif monitor == 'val_loss':
        return ModelCheckpoint(monitor=monitor,
                                dirpath=f'{args.CKPT_PATH}/{args.LOG_NAME}/fold_{fold + 1}',
                                filename=f'{args.task}_S{args.target_subject:02d}_' + '{epoch:02d}-{val_loss:.3f}',
                                save_top_k=3,
                                mode='min')
    else:
        return None


def get_callbacks(fold: int, monitor: str, args):
    checkpoint_callback = get_checkpoint_callback(fold=fold, monitor=monitor, args=args)
    lr_logger=LearningRateMonitor(logging_interval='epoch')
    return [checkpoint_callback, lr_logger]

